import { MigrationInterface, QueryRunner } from 'typeorm'

export class ReworkAddressToJsonb1748868541001 implements MigrationInterface {
  name = 'ReworkAddressToJsonb1748868541001'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contact" DROP COLUMN "balance_amount"`)
    await queryRunner.query(`ALTER TABLE "contact" DROP COLUMN "balance_currency"`)
    await queryRunner.query(`DROP TYPE "public"."currency"`)
    await queryRunner.query(`ALTER TABLE "contact" ADD "balance" jsonb`)

    // Add address columns to JSONB
    await queryRunner.query(`ALTER TABLE "contact" ADD "address" jsonb`)

    // Migrate existing data to the new JSONB column
    await queryRunner.query(`
      UPDATE "contact"
      SET "address" = jsonb_build_object(
        'country', COALESCE("address_country", ''),
        'city', COALESCE("address_city", ''),
        'postalCode', COALESCE("address_postal_code", ''),
        'streetName', COALESCE("address_street_name", ''),
        'streetNumber', COALESCE("address_street_number", ''),
        'unit', COALESCE("address_unit", ''),
        'coordinates', CASE
          WHEN "address_coordinates" IS NOT NULL
          THEN jsonb_build_object(
            'latitude', ST_Y("address_coordinates"),
            'longitude', ST_X("address_coordinates")
          )
          ELSE NULL
        END
      )
      WHERE "address" IS NULL
    `)

    // Remove old address columns
    await queryRunner.query(`ALTER TABLE "contact" DROP COLUMN "address_country"`)
    await queryRunner.query(`ALTER TABLE "contact" DROP COLUMN "address_city"`)
    await queryRunner.query(`ALTER TABLE "contact" DROP COLUMN "address_postal_code"`)
    await queryRunner.query(`ALTER TABLE "contact" DROP COLUMN "address_street_name"`)
    await queryRunner.query(`ALTER TABLE "contact" DROP COLUMN "address_street_number"`)
    await queryRunner.query(`ALTER TABLE "contact" DROP COLUMN "address_unit"`)
    await queryRunner.query(`ALTER TABLE "contact" DROP COLUMN "address_coordinates"`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {

  }
}
