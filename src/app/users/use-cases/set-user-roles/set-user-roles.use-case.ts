import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { Any, DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { TypesenseCollectionName } from '../../../../modules/typesense/collections/typesense-collection-name.enum.js'
import { TypesenseCollectionService } from '../../../../modules/typesense/services/typesense-collection.service.js'
import { UserRole } from '../../../roles/entities/user-role.entity.js'
import { User } from '../../entities/user.entity.js'
import { UserCache } from '../../cache/user-cache.js'
import { UserUuid } from '../../entities/user.uuid.js'
import { UserNotFoundError } from '../../errors/user-not-found.error.js'
import type { SetUserRolesCommand } from './set-user-roles.command.js'

@Injectable()
export class SetUserRolesUseCase {
  constructor (
    @InjectRepository(UserRole)
    private userRoleRepository: Repository<UserRole>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
    private readonly userCache: UserCache,
    private readonly typesenseService: TypesenseCollectionService
  ) {}

  async changeRoles (userUuid: UserUuid, dto: SetUserRolesCommand): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { uuid: userUuid },
      relations: { userRoles: { role: true } }
    })

    if (user === null) {
      throw new UserNotFoundError(userUuid)
    }

    assert(user.userRoles != null)

    const existingRoleUuids = user.userRoles.map(userRole => userRole.roleUuid)

    const rolesToAdd = dto.roleUuids.filter(roleUuid => !existingRoleUuids.includes(roleUuid))
      .map(roleUuid => this.userRoleRepository.create({
        userUuid,
        roleUuid
      }))

    const rolesToRemove = existingRoleUuids.filter(roleUuid => !dto.roleUuids.includes(roleUuid))

    await transaction(this.dataSource, async () => {
      await this.userRoleRepository.insert(rolesToAdd)

      await this.userRoleRepository.delete({
        userUuid,
        roleUuid: Any(rolesToRemove)
      })
    })

    await this.userCache.setUserRoles(userUuid, dto.roleUuids)
    await this.typesenseService.importManually(TypesenseCollectionName.USER, [user])
  }
}
