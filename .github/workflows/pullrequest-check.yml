---
name: PR Check

on:  # yamllint disable-line rule:truthy
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
    branches:
      - main

jobs:
  lint-build-test:
    uses: wisemen-digital/devops-github-actions/.github/workflows/node-build-and-test.yml@main
    if: github.event.pull_request.draft == false
    with:
      test-postgres-image: timescale/timescaledb-ha:pg16
      test-typesense-enabled: true
      test-typesense-image: 'typesense/typesense:29.0'
      test-redis-enabled: true
      test-nats-enabled: true
