services:
  postgres:
    image: timescale/timescaledb-ha:pg16
    ports:
      - "5432:5432"
    volumes:
      - ./.postgres:/home/<USER>/pgdata/data
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: test_db

  zitadel:
    # The user should have the permission to write to ./machinekey
    user: "${UID:-1000}"
    restart: 'always'
    image: 'ghcr.io/zitadel/zitadel:latest'
    command: 'start-from-init --masterkey "MasterkeyNeedsToHave32Characters" --tlsMode disabled'
    environment:
      ZITADEL_DATABASE_POSTGRES_HOST: postgres
      ZITADEL_DATABASE_POSTGRES_PORT: 5432
      ZITADEL_DATABASE_POSTGRES_DATABASE: test_db
      ZITADEL_DATABASE_POSTGRES_USER_USERNAME: zitadel
      ZITADEL_DATABASE_POSTGRES_USER_PASSWORD: zitadel
      ZITADEL_DATABASE_POSTGRES_USER_SSL_MODE: disable
      ZITADEL_DATABASE_POSTGRES_ADMIN_USERNAME: postgres
      ZITADEL_DATABASE_POSTGRES_ADMIN_PASSWORD: password
      ZITADEL_DATABASE_POSTGRES_ADMIN_SSL_MODE: disable
      ZITADEL_EXTERNALSECURE: false
      ZITADEL_FIRSTINSTANCE_MACHINEKEYPATH: /machinekey/zitadel-admin-sa.json
      ZITADEL_FIRSTINSTANCE_ORG_MACHINE_MACHINE_USERNAME: zitadel-admin-sa
      ZITADEL_FIRSTINSTANCE_ORG_MACHINE_MACHINE_NAME: Admin
      ZITADEL_FIRSTINSTANCE_ORG_MACHINE_MACHINEKEY_TYPE: 1
    ports:
      - '8080:8080'
    volumes:
      - ./.machinekey:/machinekey

  typesense:
    image: typesense/typesense:29.0
    ports:
      - "8108:8108"
    volumes:
      - ./.typesense:/data/typesense
    environment:
      TYPESENSE_DATA_DIR: /data
      TYPESENSE_API_KEY: api_key

  typesense_dashboard:
    image: ghcr.io/bfritscher/typesense-dashboard:latest
    restart: on-failure
    volumes:
      - ./.typesense-dashboard/config.json:/srv/config.json
    ports:
      - '8188:80'

  nats:
    image: nats:latest
    ports:
      - "4222:4222"
    volumes:
      - ./.nats:/data/nats
      - ./nats.conf:/data/nats.conf
    command:
      - "--config=/data/nats.conf"
      - "--debug"
      - "--http_port=8222"
      - "--js"
      - "--sd=/nats/data"

  redis:
    image: redis
    ports:
      - "6379:6379"

  s3service:
    image: quay.io/minio/minio:latest
    command: server --console-address ":9001" /data
    environment:
      MINIO_ROOT_USER: "admin"
      MINIO_ROOT_PASSWORD: "password"
    volumes:
      - "./.minio:/data"
    ports:
      - "0.0.0.0:9000:9000"
      - "9001:9001"
    entrypoint: >
      /bin/sh -c '
        isAlive() { curl -sf http://127.0.0.1:9000/minio/health/live; }    # check if Minio is alive
        minio $0 "$@" --quiet & echo $! > /tmp/minio.pid                   # start Minio in the background
        while ! isAlive; do sleep 0.1; done                                # wait until Minio is alive
        mc alias set minio http://127.0.0.1:9000 admin password            # setup Minio client
        mc mb minio/test-bucket || true                                    # create a test bucket
        mc anonymous set public minio/test-bucket                          # make the test bucket public
        kill -s INT $(cat /tmp/minio.pid) && rm /tmp/minio.pid             # stop Minio
        while isAlive; do sleep 0.1; done                                  # wait until Minio is stopped
        exec minio $0 "$@"                                                 # start Minio in the foreground
      '
