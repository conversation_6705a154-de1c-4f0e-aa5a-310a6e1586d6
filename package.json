{"packageManager": "pnpm@10.12.2", "name": "nestjs-example", "version": "0.1.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "type": "module", "scripts": {"clean": "rm -rf ./dist", "build": "nest build", "start": "node dist/src/entrypoints/api.js", "start:dev": "nest start --exec \"node --env-file=.env\" --watch", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint": "eslint --cache", "test:setup": "node --env-file=.env.test dist/test/setup/global-setup.js", "test:run": "node --enable-source-maps --experimental-test-isolation=none --env-file=.env.test --test \"**/*.test.js\"", "test:pipeline": "pnpm test:setup && pnpm test:run", "test:all": "pnpm clean && pnpm build && pnpm test:setup && pnpm test:run", "test:one": "pnpm clean && pnpm build && node --test --env-file=.env.test --experimental-test-isolation=none --test-reporter=spec", "typeorm": "pnpm clean && pnpm build && node --env-file=.env ./node_modules/typeorm/cli -d ./dist/src/sql/sources/main.js", "translate": "tsc --build tsconfig.translate.json && node --env-file=.env.test dist/entrypoints/generate-translations.js", "jetstream:migration:create": "node --env-file=.env dist/src/modules/nats/nats-application/streams/create-jetstream-migration.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.832.0", "@aws-sdk/lib-storage": "^3.832.0", "@aws-sdk/s3-request-presigner": "^3.832.0", "@connectrpc/connect": "^2.0.2", "@connectrpc/connect-node": "^2.0.2", "@nats-io/jetstream": "^3.0.2", "@nats-io/jwt": "0.0.10-5", "@nats-io/kv": "^3.0.2", "@nats-io/nkeys": "^2.0.3", "@nats-io/services": "^3.0.2", "@nats-io/transport-node": "^3.0.2", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/platform-express": "^11.1.3", "@nestjs/platform-ws": "^11.1.3", "@nestjs/swagger": "^11.2.0", "@nestjs/websockets": "^11.1.3", "@onesignal/node-onesignal": "5.1.0-beta1", "@opentelemetry/api": "^1.9.0", "@opentelemetry/api-logs": "^0.202.0", "@opentelemetry/exporter-logs-otlp-http": "^0.202.0", "@opentelemetry/exporter-metrics-otlp-proto": "^0.202.0", "@opentelemetry/exporter-trace-otlp-http": "^0.202.0", "@opentelemetry/instrumentation": "^0.202.0", "@opentelemetry/instrumentation-aws-sdk": "^0.54.0", "@opentelemetry/instrumentation-express": "^0.51.0", "@opentelemetry/instrumentation-http": "^0.202.0", "@opentelemetry/instrumentation-nestjs-core": "^0.48.0", "@opentelemetry/instrumentation-pg": "^0.54.0", "@opentelemetry/instrumentation-redis-4": "^0.49.0", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-logs": "^0.202.0", "@opentelemetry/sdk-metrics": "^2.0.1", "@opentelemetry/sdk-node": "^0.202.0", "@opentelemetry/sdk-trace-base": "^2.0.1", "@sentry/nestjs": "^9.30.0", "@wisemen/app-container": "^2.1.17", "@wisemen/coordinates": "0.0.4", "@wisemen/decorators": "^0.0.6", "@wisemen/monetary": "^0.2.28", "@wisemen/nestjs-typeorm": "^0.0.23", "@wisemen/one-of": "^0.0.5", "@wisemen/pagination": "^0.0.5", "@wisemen/pgboss-nestjs-job": "^1.0.6", "@wisemen/time": "^0.0.24", "@wisemen/validators": "^0.0.14", "@wisemen/wise-date": "^0.0.7", "@wisemen/opentelemetry": "^0.0.1", "@zitadel/client": "^1.2.0", "@zitadel/proto": "1.2.0", "axios": "^1.10.0", "change-case": "^5.4.4", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dayjs": "^1.11.13", "handlebars": "^4.7.8", "joi": "^17.13.3", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "nestjs-i18n": "^10.5.1", "pg": "^8.16.2", "pg-boss": "^10.3.2", "qs": "^6.14.0", "redis": "^5.5.6", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "twilio": "^5.7.1", "typeorm": "^0.3.25", "typesense": "^2.0.3", "ws": "^8.18.2", "yargs": "^18.0.0"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/testing": "^11.1.3", "@types/express": "^5.0.3", "@types/node": "^24.0.3", "@types/qs": "^6.14.0", "@types/sinon": "^17.0.4", "@types/supertest": "^6.0.3", "@types/ws": "^8.18.1", "@types/yargs": "^17.0.33", "@wisemen/eslint-config-nestjs": "^0.1.7", "eslint": "9.29.0", "eslint-plugin-unicorn": "59.0.1", "expect": "^30.0.2", "sinon": "^21.0.0", "supertest": "^7.1.1", "typescript": "^5.8.3"}}